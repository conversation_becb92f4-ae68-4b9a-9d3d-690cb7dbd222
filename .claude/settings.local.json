{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(chmod:*)", "Bash(docker buildx build:*)", "<PERSON><PERSON>(docker pull:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(echo:*)", "Bash(echo \"http_proxy: $http_proxy\")", "Bash(./build-docker.sh:*)", "Bash(ls:*)", "Bash(unset HTTP_PROXY)", "Bash(unset:*)", "Bash(unset http_proxy)", "<PERSON><PERSON>(unset https_proxy)", "Bash(osascript:*)", "<PERSON><PERSON>(open:*)", "<PERSON><PERSON>(mv:*)", "mcp__clear-thought__sequentialthinking", "Bash(rm:*)", "Bash(ping:*)", "<PERSON><PERSON>(nslookup:*)", "Bash(nc:*)", "<PERSON><PERSON>(env)", "<PERSON><PERSON>(mkdir:*)", "Bash(bash:*)", "mcp__interactive-feedback__interactive_feedback", "<PERSON><PERSON>(python:*)", "Bash(grep:*)", "Bash(sqlite3:*)", "Bash(./deploy.sh)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(comm:*)", "Bash(for name in \"box.mp3\" \"game.mp3\" \"orange.mp3\")", "Bash(do echo \"=== $name 的重复文件 ===\")", "Bash(done)", "Bash(for name in \"diploma.jpg\" \"fishing.jpg\" \"ill.jpg\" \"player.jpg\" \"pool.jpg\" \"swimming.jpg\")", "Bash(kill:*)", "Bash(pip install:*)"], "deny": []}}